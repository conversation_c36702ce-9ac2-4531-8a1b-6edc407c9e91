"""
Script to fetch dataset "test123", filter records with tag "test123", 
and create a new dataset with the filtered records
"""

import braintrust
from typing import List, Dict, Any, Optional

def fetch_and_filter_dataset(
    source_project: str = "test123",
    source_dataset: str = "test123", 
    filter_tag: str = "test123"
) -> List[Dict[str, Any]]:
    """
    Fetch dataset and filter records by tag
    
    Args:
        source_project: Project containing the source dataset
        source_dataset: Name of the source dataset
        filter_tag: Tag to filter by
        
    Returns:
        List of filtered records
    """
    try:
        print(f"Fetching dataset '{source_dataset}' from project '{source_project}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=source_project, name=source_dataset)
        
        # Get dataset summary
        summary = dataset.summarize()
        print(f"Source dataset summary: {summary}")
        
        # Fetch and filter records
        print(f"\nFiltering records with tag '{filter_tag}'...")
        filtered_records = []
        total_records = 0
        
        for record in dataset:
            total_records += 1
            record_tags = record.get('tags', [])
            
            # Check if the filter tag is in the record's tags
            if record_tags and filter_tag in record_tags:
                filtered_records.append(record)
                print(f"✓ Found matching record {len(filtered_records)}: ID={record.get('id', 'N/A')}")
        
        print(f"\nFiltering complete:")
        print(f"  Total records in source: {total_records}")
        print(f"  Records with tag '{filter_tag}': {len(filtered_records)}")
        
        return filtered_records
        
    except Exception as e:
        print(f"Error fetching and filtering dataset: {e}")
        return []

def create_new_dataset(
    filtered_records: List[Dict[str, Any]],
    target_project: str,
    target_dataset: str,
    description: Optional[str] = None
) -> bool:
    """
    Create a new dataset with filtered records
    
    Args:
        filtered_records: List of records to insert
        target_project: Project for the new dataset
        target_dataset: Name for the new dataset
        description: Optional description for the dataset
        
    Returns:
        True if successful, False otherwise
    """
    if not filtered_records:
        print("No filtered records to create dataset with.")
        return False
    
    try:
        print(f"\nCreating new dataset '{target_dataset}' in project '{target_project}'...")
        
        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=target_project, name=target_dataset)
        
        # Insert filtered records
        inserted_count = 0
        for record in filtered_records:
            try:
                # Prepare record data for insertion
                insert_data = {}
                
                # Copy core fields
                if 'input' in record:
                    insert_data['input'] = record['input']
                
                # Handle expected vs output field
                if 'expected' in record:
                    insert_data['expected'] = record['expected']
                elif 'output' in record:
                    insert_data['expected'] = record['output']  # Convert output to expected
                
                # Copy metadata and tags
                if 'metadata' in record and record['metadata']:
                    insert_data['metadata'] = record['metadata']
                
                if 'tags' in record and record['tags']:
                    insert_data['tags'] = record['tags']
                
                # Insert the record
                record_id = new_dataset.insert(**insert_data)
                inserted_count += 1
                print(f"  Inserted record {inserted_count}: {record_id}")
                
            except Exception as e:
                print(f"  Error inserting record: {e}")
                continue
        
        # Flush to ensure all records are saved
        new_dataset.flush()
        
        print(f"\nDataset creation complete:")
        print(f"  Successfully inserted: {inserted_count} records")
        print(f"  Dataset name: '{target_dataset}'")
        print(f"  Project: '{target_project}'")
        
        # Show summary of new dataset
        new_summary = new_dataset.summarize()
        print(f"  New dataset summary: {new_summary}")
        
        return True
        
    except Exception as e:
        print(f"Error creating new dataset: {e}")
        return False

def preview_filtered_records(records: List[Dict[str, Any]], max_preview: int = 3) -> None:
    """
    Preview the filtered records before creating the dataset
    
    Args:
        records: List of filtered records
        max_preview: Maximum number of records to preview
    """
    if not records:
        print("No records to preview.")
        return
    
    print(f"\n=== Preview of Filtered Records (showing up to {max_preview}) ===")
    
    for i, record in enumerate(records[:max_preview]):
        print(f"\nRecord {i+1}:")
        print(f"  ID: {record.get('id', 'N/A')}")
        print(f"  Input: {record.get('input', 'N/A')}")
        print(f"  Expected: {record.get('expected', 'N/A')}")
        print(f"  Output: {record.get('output', 'N/A')}")
        print(f"  Metadata: {record.get('metadata', 'N/A')}")
        print(f"  Tags: {record.get('tags', 'N/A')}")
    
    if len(records) > max_preview:
        print(f"\n... and {len(records) - max_preview} more records")

def main():
    """
    Main function to orchestrate the filtering and dataset creation process
    """
    print("=== Dataset Filter and Create Tool ===\n")
    
    # Configuration
    SOURCE_PROJECT = "test123"      # Project containing source dataset
    SOURCE_DATASET = "test123"      # Source dataset name
    FILTER_TAG = "test123"          # Tag to filter by
    TARGET_PROJECT = "test123"      # Project for new dataset (can be same or different)
    TARGET_DATASET = "test123_filtered"  # Name for new filtered dataset
    
    print("Configuration:")
    print(f"  Source: {SOURCE_PROJECT}/{SOURCE_DATASET}")
    print(f"  Filter tag: '{FILTER_TAG}'")
    print(f"  Target: {TARGET_PROJECT}/{TARGET_DATASET}")
    print()
    
    # Step 1: Fetch and filter records
    filtered_records = fetch_and_filter_dataset(
        source_project=SOURCE_PROJECT,
        source_dataset=SOURCE_DATASET,
        filter_tag=FILTER_TAG
    )
    
    if not filtered_records:
        print("\n❌ No records found with the specified tag. Exiting.")
        return
    
    # Step 2: Preview filtered records
    preview_filtered_records(filtered_records)
    
    # Step 3: Ask for confirmation (in a real script, you might want user input)
    print(f"\n📋 Ready to create new dataset with {len(filtered_records)} filtered records.")
    
    # For automation, we'll proceed automatically. 
    # In interactive use, you could add: input("Press Enter to continue or Ctrl+C to cancel...")
    
    # Step 4: Create new dataset
    success = create_new_dataset(
        filtered_records=filtered_records,
        target_project=TARGET_PROJECT,
        target_dataset=TARGET_DATASET,
        description=f"Filtered dataset containing records with tag '{FILTER_TAG}'"
    )
    
    if success:
        print(f"\n✅ Successfully created filtered dataset '{TARGET_DATASET}'!")
        print(f"   You can now use this dataset in evaluations or view it in the Braintrust UI.")
    else:
        print(f"\n❌ Failed to create filtered dataset.")

if __name__ == "__main__":
    main()
