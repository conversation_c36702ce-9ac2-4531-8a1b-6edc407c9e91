"""
Debug script to inspect dataset records and their tags
"""

import braintrust
from typing import Dict, Any, List

def debug_dataset_tags(project: str = "pedro-project1", dataset_name: str = "pedro-project1"):
    """
    Debug function to inspect all records and their tags in detail
    """
    try:
        print(f"=== Debugging Dataset Tags ===")
        print(f"Project: {project}")
        print(f"Dataset: {dataset_name}")
        print()
        
        # Initialize dataset
        dataset = braintrust.init_dataset(project=project, name=dataset_name)
        
        # Get summary
        summary = dataset.summarize()
        print(f"Dataset summary: {summary}")
        print()
        
        # Analyze all records
        all_tags = set()
        records_with_tags = 0
        records_without_tags = 0
        tag_counts = {}
        
        print("=== Record Analysis ===")
        
        for i, record in enumerate(dataset, 1):
            print(f"\nRecord {i}:")
            print(f"  ID: {record.get('id', 'N/A')}")
            print(f"  Full record keys: {list(record.keys())}")
            
            # Check tags field
            tags = record.get('tags')
            print(f"  Tags field: {tags} (type: {type(tags)})")
            
            if tags:
                records_with_tags += 1
                if isinstance(tags, list):
                    for tag in tags:
                        all_tags.add(tag)
                        tag_counts[tag] = tag_counts.get(tag, 0) + 1
                        print(f"    - Tag: '{tag}' (type: {type(tag)})")
                elif isinstance(tags, str):
                    # Sometimes tags might be stored as a single string
                    all_tags.add(tags)
                    tag_counts[tags] = tag_counts.get(tags, 0) + 1
                    print(f"    - Single tag: '{tags}' (type: {type(tags)})")
                else:
                    print(f"    - Unexpected tag format: {tags}")
            else:
                records_without_tags += 1
                print(f"    - No tags")
            
            # Show other fields for context
            print(f"  Input: {record.get('input', 'N/A')}")
            if 'expected' in record:
                print(f"  Expected: {record.get('expected')}")
            if 'output' in record:
                print(f"  Output: {record.get('output')}")
            if 'metadata' in record:
                print(f"  Metadata: {record.get('metadata')}")
        
        # Summary
        print(f"\n=== Summary ===")
        print(f"Total records: {i}")
        print(f"Records with tags: {records_with_tags}")
        print(f"Records without tags: {records_without_tags}")
        print(f"Unique tags found: {len(all_tags)}")
        
        if all_tags:
            print(f"\nAll unique tags:")
            for tag in sorted(all_tags):
                count = tag_counts.get(tag, 0)
                print(f"  - '{tag}' (appears in {count} records)")
        
        # Check specifically for "test123" tag
        print(f"\n=== Checking for 'test123' tag ===")
        test123_count = tag_counts.get('test123', 0)
        if test123_count > 0:
            print(f"✅ Found 'test123' tag in {test123_count} records")
        else:
            print(f"❌ 'test123' tag not found")
            
            # Check for similar tags
            similar_tags = [tag for tag in all_tags if 'test' in tag.lower() or '123' in tag]
            if similar_tags:
                print(f"Similar tags found: {similar_tags}")
            else:
                print("No similar tags found")
        
        return all_tags, tag_counts
        
    except Exception as e:
        print(f"Error debugging dataset: {e}")
        import traceback
        traceback.print_exc()
        return set(), {}

def test_tag_filtering(project: str = "pedro-project1", dataset_name: str = "pedro-project1"):
    """
    Test different tag filtering approaches
    """
    print(f"\n=== Testing Tag Filtering Approaches ===")
    
    try:
        dataset = braintrust.init_dataset(project=project, name=dataset_name)
        
        # Test different filtering methods
        test_tags = ['test123', 'Test123', 'TEST123']  # Case variations
        
        for test_tag in test_tags:
            print(f"\nTesting filter for tag: '{test_tag}'")
            matches = 0
            
            for record in dataset:
                tags = record.get('tags', [])
                
                # Method 1: Exact match
                if tags and test_tag in tags:
                    matches += 1
                    print(f"  ✅ Exact match found in record {record.get('id', 'N/A')}")
                
                # Method 2: Case-insensitive match
                elif tags and any(tag.lower() == test_tag.lower() for tag in tags if isinstance(tag, str)):
                    print(f"  ⚠️  Case-insensitive match found in record {record.get('id', 'N/A')}")
                
                # Method 3: Partial match
                elif tags and any(test_tag.lower() in tag.lower() for tag in tags if isinstance(tag, str)):
                    print(f"  🔍 Partial match found in record {record.get('id', 'N/A')}")
            
            print(f"  Total exact matches for '{test_tag}': {matches}")
    
    except Exception as e:
        print(f"Error testing tag filtering: {e}")

if __name__ == "__main__":
    # Run the debug analysis
    all_tags, tag_counts = debug_dataset_tags()
    
    # Test filtering approaches
    test_tag_filtering()
    
    print(f"\n=== Recommendations ===")
    if 'test123' in all_tags:
        print("✅ The 'test123' tag exists. The filtering should work.")
    else:
        print("❌ The 'test123' tag was not found. Possible issues:")
        print("1. The tag might have different casing (Test123, TEST123)")
        print("2. The tag might have extra spaces or characters")
        print("3. The tag might be stored in a different format")
        print("4. The records might not have the expected tag")
        
        if all_tags:
            print(f"\nAvailable tags to choose from: {sorted(all_tags)}")
        else:
            print("\nNo tags found in any records.")
