# EvalCase Examples

This directory contains simple examples showing how to use `EvalCase` with the Braintrust Python SDK.

## Prerequisites

```bash
pip install braintrust autoevals
```

You'll also need to set up your Braintrust API key:
```bash
export BRAINTRUST_API_KEY="your-api-key-here"
```

## Examples

### 1. Simple EvalCase Example (`simple_evalcase_example.py`)

This example shows the basics:
- Creating `EvalCase` objects with input, expected output, metadata, and tags
- Two different tasks: math operations and text processing
- Using built-in scorers (`NumericDiff`, `Lev<PERSON>htein`) and custom scorers

**Key concepts demonstrated:**
- Basic `EvalCase` structure
- Adding metadata and tags to test cases
- Using multiple scoring functions
- Accessing results after evaluation

```bash
python simple_evalcase_example.py
```

### 2. Advanced EvalCase Example (`advanced_evalcase_example.py`)

This example shows more sophisticated usage:
- Three different ways to create test data
- Using evaluation hooks to log metadata during task execution
- Mixing `EvalCase` objects with plain dictionaries
- Generator functions for dynamic test case creation
- Custom scoring functions

**Key concepts demonstrated:**
- Multiple patterns for creating test data
- Using `hooks` parameter in task functions
- Generator functions for test cases
- Mixed data types (EvalCase + plain dicts)
- Custom scorer implementation

```bash
python advanced_evalcase_example.py
```

## Key Takeaways

### EvalCase Structure
```python
EvalCase(
    input={"your": "input_data"},           # Required: input to your task
    expected="expected_output",             # Optional: expected result
    metadata={"key": "value"},              # Optional: additional data
    tags=["tag1", "tag2"]                   # Optional: categorization tags
)
```

### Different Ways to Provide Data to Eval()

1. **List of EvalCase objects:**
   ```python
   data=[EvalCase(...), EvalCase(...)]
   ```

2. **Function returning EvalCase objects:**
   ```python
   data=lambda: [EvalCase(...), EvalCase(...)]
   ```

3. **Generator function:**
   ```python
   def generate_cases():
       yield EvalCase(...)
   data=generate_cases
   ```

4. **Mixed (EvalCase + plain dicts):**
   ```python
   data=[EvalCase(...), {"input": "...", "expected": "..."}]
   ```

### Task Functions

Your task function can take just the input:
```python
def simple_task(input_data):
    return process(input_data)
```

Or input + hooks for more advanced usage:
```python
def advanced_task(input_data, hooks):
    hooks.metadata["processing_time"] = time.time()
    return process(input_data)
```

### Results

After running `Eval()`, you get results with:
- `result.input` - Original input
- `result.output` - Task function output  
- `result.expected` - Expected output (if provided)
- `result.tags` - Tags from EvalCase
- `result.metadata` - Metadata from EvalCase + any added by hooks
- `result.scores` - Scores from all scoring functions
- `result.error` - Any errors that occurred

## Next Steps

- Try modifying the examples with your own tasks and test cases
- Experiment with different scoring functions from `autoevals`
- Add more complex metadata and tags for better test organization
- Use the Braintrust web interface to visualize your evaluation results
