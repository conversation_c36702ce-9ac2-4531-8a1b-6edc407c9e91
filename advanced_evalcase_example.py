"""
Advanced example showing different ways to use EvalCase
"""

from typing import List, Iterator
from braintrust import Eval, EvalCase
from autoevals import Levenshtein

# Example task: Simple chatbot that responds to questions
def chatbot_task(input_data, hooks):
    """
    Simple chatbot task that uses hooks to log metadata
    """
    question = input_data["question"]
    context = input_data.get("context", "")
    
    # Log some metadata using hooks
    hooks.metadata["question_length"] = len(question)
    hooks.metadata["has_context"] = bool(context)
    
    # Simple rule-based responses
    question_lower = question.lower()
    
    if "hello" in question_lower or "hi" in question_lower:
        response = "Hello! How can I help you today?"
    elif "weather" in question_lower:
        response = "I don't have access to current weather data, but you can check a weather app!"
    elif "time" in question_lower:
        response = "I don't have access to the current time, but you can check your device's clock!"
    elif context:
        response = f"Based on the context about '{context}', I'd say: {question}"
    else:
        response = "I'm not sure how to answer that. Can you provide more details?"
    
    return response

# Method 1: Using explicit EvalCase objects in a list
def create_explicit_test_cases() -> List[EvalCase]:
    """Create test cases using explicit EvalCase constructor"""
    return [
        EvalCase(
            input={"question": "Hello there!"},
            expected="Hello! How can I help you today?",
            metadata={"test_category": "greeting", "difficulty": "easy"},
            tags=["greeting", "basic"]
        ),
        EvalCase(
            input={"question": "What's the weather like?"},
            expected="I don't have access to current weather data, but you can check a weather app!",
            metadata={"test_category": "weather", "difficulty": "medium"},
            tags=["weather", "information"]
        ),
        EvalCase(
            input={
                "question": "Tell me about this topic",
                "context": "machine learning"
            },
            expected="Based on the context about 'machine learning', I'd say: Tell me about this topic",
            metadata={"test_category": "context", "difficulty": "hard"},
            tags=["context", "complex"]
        ),
    ]

# Method 2: Using a generator function
def generate_test_cases() -> Iterator[EvalCase]:
    """Generate test cases dynamically using a generator"""
    
    # Base test cases
    base_cases = [
        ("Hi!", "greeting", ["greeting"]),
        ("What time is it?", "time", ["time", "information"]),
        ("Random question", "unknown", ["unknown"]),
    ]
    
    for question, category, tags in base_cases:
        yield EvalCase(
            input={"question": question},
            expected=None,  # We'll let the evaluation determine if it's correct
            metadata={"test_category": category, "generated": True},
            tags=tags + ["generated"]
        )

# Method 3: Mixed approach - combining EvalCase and plain dicts
def create_mixed_test_cases():
    """Mix EvalCase objects with plain dictionaries"""
    return [
        # Explicit EvalCase
        EvalCase(
            input={"question": "Hello world!"},
            expected="Hello! How can I help you today?",
            tags=["greeting", "explicit"]
        ),
        
        # Plain dictionary (will be auto-converted to EvalCase)
        {
            "input": {"question": "What's the weather?"},
            "expected": "I don't have access to current weather data, but you can check a weather app!",
            "tags": ["weather", "auto_converted"]
        },
        
        # Another EvalCase with rich metadata
        EvalCase(
            input={"question": "Help me understand", "context": "Python programming"},
            expected="Based on the context about 'Python programming', I'd say: Help me understand",
            metadata={
                "complexity": "high",
                "requires_context": True,
                "test_author": "example_script"
            },
            tags=["context", "programming", "complex"]
        ),
    ]

# Custom scorer that checks if response is helpful
def helpfulness_scorer(input, output, expected):
    """
    Custom scorer that rates helpfulness of responses
    """
    output_lower = output.lower()
    
    # Simple heuristics for helpfulness
    helpful_phrases = [
        "i don't have access",
        "you can check",
        "how can i help",
        "based on the context"
    ]
    
    helpfulness_score = 0
    for phrase in helpful_phrases:
        if phrase in output_lower:
            helpfulness_score += 0.25
    
    # Cap at 1.0
    return min(helpfulness_score, 1.0)

if __name__ == "__main__":
    print("=== Advanced EvalCase Example ===\n")
    
    # Run evaluation with explicit EvalCase objects
    print("1. Running evaluation with explicit EvalCase objects...")
    explicit_results = Eval(
        "Chatbot Explicit Cases",
        data=create_explicit_test_cases(),
        task=chatbot_task,
        scores=[Levenshtein, helpfulness_scorer],
        experiment_name="Explicit EvalCase Test"
    )
    
    print(f"   Completed {len(explicit_results.results)} test cases\n")
    
    # Run evaluation with generated test cases
    print("2. Running evaluation with generated test cases...")
    generated_results = Eval(
        "Chatbot Generated Cases",
        data=generate_test_cases,  # Note: passing the function, not calling it
        task=chatbot_task,
        scores=[helpfulness_scorer],
        experiment_name="Generated EvalCase Test"
    )
    
    print(f"   Completed {len(generated_results.results)} test cases\n")
    
    # Run evaluation with mixed test cases
    print("3. Running evaluation with mixed test cases...")
    mixed_results = Eval(
        "Chatbot Mixed Cases",
        data=create_mixed_test_cases(),
        task=chatbot_task,
        scores=[Levenshtein, helpfulness_scorer],
        experiment_name="Mixed EvalCase Test"
    )
    
    print(f"   Completed {len(mixed_results.results)} test cases\n")
    
    # Show detailed results from one evaluation
    print("=== Sample Results from Mixed Cases ===")
    for i, result in enumerate(mixed_results.results):
        print(f"\nTest Case {i+1}:")
        print(f"  Input: {result.input}")
        print(f"  Expected: {result.expected}")
        print(f"  Actual Output: {result.output}")
        print(f"  Tags: {result.tags}")
        print(f"  Metadata: {result.metadata}")
        print(f"  Scores: {result.scores}")
        
        if result.error:
            print(f"  Error: {result.error}")
