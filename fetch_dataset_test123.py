"""
<PERSON><PERSON><PERSON> to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import Eval<PERSON>ase
from typing import List, Dict, Any

def fetch_dataset_test123(project_name, dataset_name) -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
        records = []
        for record in dataset:
            records.append(record)

        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []
    
def filter_records_by_tag(records: List[Dict[str, Any]], target_tag: str) -> List[Dict[str, Any]]:
    """
    Filter records that contain a specific tag

    Args:
        records: List of dataset records
        target_tag: The tag to filter by

    Returns:
        List of records that contain the target tag
    """
    filtered_records = []

    for record in records:
        tags = record.get('tags', [])
        # Check if tags exist and if target_tag is in the list of tags
        if tags and target_tag in tags:
            filtered_records.append(record)

    return filtered_records

def create_dataset_from_records(
    records: List[Dict[str, Any]],
    project_name: str,
    new_dataset_name: str,
    description: str = None
) -> None:
    """
    Create a new dataset from a list of records

    Args:
        records: List of dataset records to insert
        project_name: Project name for the new dataset
        new_dataset_name: Name for the new dataset
        description: Optional description for the dataset
    """
    try:
        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=project_name, name=new_dataset_name)

        # Insert each record
        inserted_count = 0
        for record in records:
            try:
                # Insert record with all its fields
                record_id = new_dataset.insert(
                    input=record.get('input'),
                    expected=record.get('expected') or record.get('output'),  # Handle both field names
                    metadata=record.get('metadata'),
                    tags=record.get('tags')
                )
                inserted_count += 1
                print(f"Inserted record {inserted_count} with ID: {record_id}")

            except Exception as e:
                print(f"Error inserting record {inserted_count + 1}: {e}")

        # Flush to ensure all records are saved
        new_dataset.flush()

        print(f"\nSuccessfully created dataset '{new_dataset_name}' with {inserted_count} records")

        return new_dataset

    except Exception as e:
        print(f"Error creating dataset: {e}")
        return None

if __name__ == "__main__":    
    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project
    dataset_name = "test123"
    
    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME,dataset_name)

    filtered_records = filter_records_by_tag(records, "test123")
    
    #if you want to create a new dataset from the filtered one
    create_dataset_from_records(filtered_records, PROJECT_NAME, "filtered_by_tag_test123")