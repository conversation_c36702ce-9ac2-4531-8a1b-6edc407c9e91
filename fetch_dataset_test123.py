"""
<PERSON><PERSON><PERSON> to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import EvalC<PERSON>, <PERSON><PERSON>
from typing import List, Dict, Any

def fetch_dataset_test123(project_name, dataset_name) -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
        records = []
        for record in dataset:
            records.append(record)

        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []
    
def filter_records_by_tag(records: List[Dict[str, Any]], target_tag: str) -> List[Dict[str, Any]]:
    """
    Filter records that contain a specific tag

    Args:
        records: List of dataset records
        target_tag: The tag to filter by

    Returns:
        List of records that contain the target tag
    """
    filtered_records = []

    for record in records:
        tags = record.get('tags', [])
        # Check if tags exist and if target_tag is in the list of tags
        if tags and target_tag in tags:
            filtered_records.append(record)

    return filtered_records

def create_dataset_from_records(
    records: List[Dict[str, Any]],
    project_name: str,
    new_dataset_name: str,
    description: str = None
) -> None:
    """
    Create a new dataset from a list of records

    Args:
        records: List of dataset records to insert
        project_name: Project name for the new dataset
        new_dataset_name: Name for the new dataset
        description: Optional description for the dataset
    """
    try:
        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=project_name, name=new_dataset_name)

        # Insert each record
        inserted_count = 0
        for record in records:
            try:
                # Insert record with all its fields
                record_id = new_dataset.insert(
                    input=record.get('input'),
                    expected=record.get('expected') or record.get('output'),  # Handle both field names
                    metadata=record.get('metadata'),
                    tags=record.get('tags')
                )
                inserted_count += 1
                print(f"Inserted record {inserted_count} with ID: {record_id}")

            except Exception as e:
                print(f"Error inserting record {inserted_count + 1}: {e}")

        # Flush to ensure all records are saved
        new_dataset.flush()

        print(f"\nSuccessfully created dataset '{new_dataset_name}' with {inserted_count} records")

        return new_dataset

    except Exception as e:
        print(f"Error creating dataset: {e}")
        return None

def ambience_scorer(input, output, expected):
    """
    Custom scorer for evaluating ambience-related responses

    Args:
        input: The input data from the record
        output: The output from the task function
        expected: The expected output from the record

    Returns:
        float: Score between 0.0 and 1.0
    """
    # Example ambience scoring logic - customize this based on your needs
    if not output or not expected:
        return 0.0

    # Convert to strings for comparison
    output_str = str(output).lower()
    expected_str = str(expected).lower()

    # Simple scoring based on ambience-related keywords
    ambience_keywords = [
        'atmosphere', 'mood', 'feeling', 'vibe', 'ambiance', 'ambience',
        'cozy', 'warm', 'comfortable', 'relaxing', 'peaceful', 'calm',
        'bright', 'dim', 'lighting', 'music', 'sound', 'quiet', 'noisy'
    ]

    # Check if output contains ambience-related terms
    output_has_ambience = any(keyword in output_str for keyword in ambience_keywords)
    expected_has_ambience = any(keyword in expected_str for keyword in ambience_keywords)

    # Basic scoring logic
    if output_str == expected_str:
        return 1.0  # Perfect match
    elif output_has_ambience and expected_has_ambience:
        return 0.8  # Both mention ambience concepts
    elif output_has_ambience or expected_has_ambience:
        return 0.6  # One mentions ambience concepts
    else:
        # Fallback to simple similarity
        common_words = set(output_str.split()) & set(expected_str.split())
        if len(common_words) > 0:
            return min(0.5, len(common_words) * 0.1)
        return 0.1  # Minimal score for any response

def run_eval_with_filtered_records(
    filtered_records: List[Dict[str, Any]],
    project_name: str,
    experiment_name: str = "Filtered Records Evaluation"
) -> None:
    """
    Run an evaluation using filtered records directly (not creating a new dataset)

    Args:
        filtered_records: List of filtered dataset records
        project_name: Project name for the evaluation
        experiment_name: Name for the experiment
    """
    if not filtered_records:
        print("❌ No filtered records provided for evaluation")
        return

    print(f"\n=== Running Evaluation with Filtered Records ===")
    print(f"Project: {project_name}")
    print(f"Experiment: {experiment_name}")
    print(f"Records to evaluate: {len(filtered_records)}")

    # Convert records to EvalCase format
    eval_cases = []
    for record in filtered_records:
        eval_case = EvalCase(
            input=record.get('input'),
            expected=record.get('expected') or record.get('output'),
            metadata=record.get('metadata'),
            tags=record.get('tags')
        )
        eval_cases.append(eval_case)

    # Define a simple task function - customize this based on your needs
    def simple_task(input_data, hooks=None):
        """
        Simple task function - replace this with your actual task logic
        """
        # Example: Echo the input with some processing
        if isinstance(input_data, dict):
            # If input is a dict, try to extract meaningful content
            if 'question' in input_data:
                return f"Response to: {input_data['question']}"
            elif 'text' in input_data:
                return f"Processed: {input_data['text']}"
            else:
                return f"Processed input: {str(input_data)}"
        else:
            return f"Processed: {str(input_data)}"

    try:
        # Run the evaluation
        results = Eval(
            project_name,
            data=eval_cases,
            task=simple_task,
            scores=[ambience_scorer],  # Use the ambience_scorer
            experiment_name=experiment_name
        )

        print(f"\n✅ Evaluation completed successfully!")
        print(f"   Total test cases: {len(results.results)}")
        print(f"   Experiment name: {experiment_name}")

        # Show some sample results
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results.results[:3], 1):  # Show first 3 results
            print(f"\nResult {i}:")
            print(f"  Input: {result.input}")
            print(f"  Expected: {result.expected}")
            print(f"  Output: {result.output}")
            print(f"  Ambience Score: {result.scores.get('ambience_scorer', 'N/A')}")
            print(f"  Tags: {result.tags}")

        if len(results.results) > 3:
            print(f"\n... and {len(results.results) - 3} more results")

        return results

    except Exception as e:
        print(f"❌ Error running evaluation: {e}")
        return None

if __name__ == "__main__":
    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project
    dataset_name = "test123"

    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME, dataset_name)
    print(f"Fetched {len(records)} total records")

    # Filter records by tag
    filtered_records = filter_records_by_tag(records, "test123")
    print(f"Filtered to {len(filtered_records)} records with 'test123' tag")

    # Run evaluation with filtered records using ambience_scorer
    if filtered_records:
        print(f"\n=== Running Evaluation with Ambience Scorer ===")
        eval_results = run_eval_with_filtered_records(
            filtered_records=filtered_records,
            project_name=PROJECT_NAME,
            experiment_name="Test123 Filtered Records - Ambience Evaluation"
        )

        if eval_results:
            print(f"\n🎉 Evaluation completed! Check your Braintrust dashboard for detailed results.")
    else:
        print("❌ No records found with 'test123' tag - cannot run evaluation")

    # Optional: Create a new dataset from the filtered records
    print(f"\n=== Optional: Creating New Dataset ===")
    create_dataset_from_records(filtered_records, PROJECT_NAME, "filtered_by_tag_test123")

    print(f"\n" + "="*60)
    print("USAGE EXAMPLES:")
    print("="*60)
    print("""
# Run evaluation with different tag filter:
records = fetch_dataset_test123('pedro-project1', 'test123')
filtered = filter_records_by_tag(records, 'important')
run_eval_with_filtered_records(
    filtered_records=filtered,
    project_name='pedro-project1',
    experiment_name='Important Records Evaluation'
)

# Customize the task function in run_eval_with_filtered_records()
# to match your specific use case and input/output format
""")