"""
Script to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import Eval<PERSON><PERSON>
from typing import List, Dict, Any

def fetch_dataset_test123(project_name: str = "test123") -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name="test123")
        
        # Get dataset summary
        print("\n=== Dataset Summary ===")
        summary = dataset.summarize()
        print(f"Dataset summary: {summary}")
        
        # Fetch and display all records
        print("\n=== Dataset Records ===")
        records = []
        record_count = 0
        
        for record in dataset:
            record_count += 1
            records.append(record)
            print(f"\nRecord {record_count}:")
            print(f"  ID: {record.get('id', 'N/A')}")
            print(f"  Input: {record.get('input', 'N/A')}")
            print(f"  Expected: {record.get('expected', 'N/A')}")
            print(f"  Output: {record.get('output', 'N/A')}")  # Some datasets use 'output' instead of 'expected'
            print(f"  Metadata: {record.get('metadata', 'N/A')}")
            print(f"  Tags: {record.get('tags', 'N/A')}")
            print(f"  Created: {record.get('created', 'N/A')}")
        
        print(f"\nTotal records found: {record_count}")
        
        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []

# def convert_to_evalcases(records: List[Dict[str, Any]]) -> List[EvalCase]:
#     """
#     Convert dataset records to EvalCase objects for use in evaluations

#     Args:
#         records: List of dataset records

#     Returns:
#         List of EvalCase objects
#     """
#     eval_cases = []

#     for record in records:
#         # Handle both 'expected' and 'output' fields (datasets can use either)
#         expected_value = record.get('expected') or record.get('output')

#         eval_case = EvalCase(
#             input=record.get('input'),
#             expected=expected_value,
#             metadata=record.get('metadata'),
#             tags=record.get('tags')
#         )
#         eval_cases.append(eval_case)

#     return eval_cases

def filter_records_by_tag(records: List[Dict[str, Any]], target_tag: str) -> List[Dict[str, Any]]:
    """
    Filter records that contain a specific tag

    Args:
        records: List of dataset records
        target_tag: The tag to filter by

    Returns:
        List of records that contain the target tag
    """
    filtered_records = []

    for record in records:
        tags = record.get('tags', [])
        # Check if tags exist and if target_tag is in the list of tags
        if tags and target_tag in tags:
            filtered_records.append(record)

    return filtered_records

def create_dataset_from_records(
    records: List[Dict[str, Any]],
    project_name: str,
    new_dataset_name: str,
    description: str = None
) -> None:
    """
    Create a new dataset from a list of records

    Args:
        records: List of dataset records to insert
        project_name: Project name for the new dataset
        new_dataset_name: Name for the new dataset
        description: Optional description for the dataset
    """
    try:
        print(f"\n=== Creating New Dataset ===")
        print(f"Project: {project_name}")
        print(f"Dataset Name: {new_dataset_name}")
        print(f"Records to insert: {len(records)}")

        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=project_name, name=new_dataset_name)

        # Insert each record
        inserted_count = 0
        for record in records:
            try:
                # Insert record with all its fields
                record_id = new_dataset.insert(
                    input=record.get('input'),
                    expected=record.get('expected') or record.get('output'),  # Handle both field names
                    metadata=record.get('metadata'),
                    tags=record.get('tags')
                )
                inserted_count += 1
                print(f"  ✓ Inserted record {inserted_count} with ID: {record_id}")

            except Exception as e:
                print(f"  ❌ Error inserting record {inserted_count + 1}: {e}")

        # Flush to ensure all records are saved
        new_dataset.flush()

        print(f"\n✅ Successfully created dataset '{new_dataset_name}' with {inserted_count} records")

        # Show summary of new dataset
        summary = new_dataset.summarize()
        print(f"New dataset summary: {summary}")

        return new_dataset

    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        return None

def use_dataset_in_evaluation(project_name: str = "test123") -> None:
    """
    Example of how to use the fetched dataset in an evaluation
    """
    from autoevals import Levenshtein
    
    print("\n=== Using Dataset in Evaluation ===")
    
    # Method 1: Use dataset directly in Eval
    print("Method 1: Using dataset directly...")
    try:
        dataset = braintrust.init_dataset(project=project_name, name="test123")
        
        # Simple task function for demonstration
        def simple_task(input_data):
            # This is just a placeholder - replace with your actual task
            if isinstance(input_data, str):
                return f"Processed: {input_data}"
            elif isinstance(input_data, dict):
                return f"Processed: {input_data}"
            else:
                return f"Processed: {str(input_data)}"
        
        # Use the dataset directly in an evaluation
        # Note: Uncomment the lines below to actually run the evaluation
        """
        from braintrust import Eval
        
        results = Eval(
            "Test123 Dataset Evaluation",
            data=dataset,  # Use dataset directly
            task=simple_task,
            scores=[Levenshtein],
            experiment_name="Fetch Test123 Dataset"
        )
        
        print(f"Evaluation completed with {len(results.results)} test cases")
        """
        
        print("Dataset is ready for evaluation (evaluation code commented out)")
        
    except Exception as e:
        print(f"Error using dataset in evaluation: {e}")
    
    # Method 2: Convert to EvalCase objects first
    print("\nMethod 2: Converting to EvalCase objects...")
    try:
        records = fetch_dataset_test123(project_name)
        if records:
            eval_cases = convert_to_evalcases(records)
            print(f"Converted {len(eval_cases)} records to EvalCase objects")
            
            # Show first EvalCase as example
            if eval_cases:
                print(f"\nFirst EvalCase:")
                first_case = eval_cases[0]
                print(f"  Input: {first_case.input}")
                print(f"  Expected: {first_case.expected}")
                print(f"  Metadata: {first_case.metadata}")
                print(f"  Tags: {first_case.tags}")
        
    except Exception as e:
        print(f"Error converting to EvalCase objects: {e}")

def fetch_filter_and_create_dataset(
    source_project: str = "test123",
    source_dataset: str = "test123",
    target_tag: str = "test123",
    new_project: str = None,
    new_dataset_name: str = None
) -> None:
    """
    Complete workflow: fetch dataset, filter by tag, create new dataset

    Args:
        source_project: Project containing the source dataset
        source_dataset: Name of the source dataset
        target_tag: Tag to filter by
        new_project: Project for new dataset (defaults to source_project)
        new_dataset_name: Name for new dataset (defaults to filtered_{source_dataset}_{target_tag})
    """
    # Set defaults
    if new_project is None:
        new_project = source_project
    if new_dataset_name is None:
        new_dataset_name = f"filtered_{source_dataset}_{target_tag}"

    print(f"\n=== Complete Workflow: Fetch, Filter, Create ===")
    print(f"Source: {source_project}/{source_dataset}")
    print(f"Filter by tag: '{target_tag}'")
    print(f"Target: {new_project}/{new_dataset_name}")

    try:
        # Step 1: Fetch all records from source dataset
        print(f"\n1. Fetching records from '{source_dataset}'...")
        dataset = braintrust.init_dataset(project=source_project, name=source_dataset)

        all_records = []
        for record in dataset:
            all_records.append(record)

        print(f"   Found {len(all_records)} total records")

        # Step 2: Filter records by tag
        print(f"\n2. Filtering records by tag '{target_tag}'...")
        filtered_records = filter_records_by_tag(all_records, target_tag)

        print(f"   Found {len(filtered_records)} records with tag '{target_tag}'")

        if not filtered_records:
            print(f"   ⚠️  No records found with tag '{target_tag}'. Available tags:")
            all_tags = set()
            for record in all_records:
                tags = record.get('tags', [])
                if tags:
                    all_tags.update(tags)
            print(f"   Available tags: {sorted(list(all_tags))}")
            return

        # Step 3: Create new dataset with filtered records
        print(f"\n3. Creating new dataset '{new_dataset_name}'...")
        new_dataset = create_dataset_from_records(
            records=filtered_records,
            project_name=new_project,
            new_dataset_name=new_dataset_name,
            description=f"Filtered from {source_dataset} by tag '{target_tag}'"
        )

        if new_dataset:
            print(f"\n✅ Workflow completed successfully!")
            print(f"   Source: {len(all_records)} records")
            print(f"   Filtered: {len(filtered_records)} records")
            print(f"   New dataset: {new_project}/{new_dataset_name}")

    except Exception as e:
        print(f"❌ Error in workflow: {e}")

def fetch_with_filtering(project_name: str = "test123") -> None:
    """
    Example of fetching dataset records with filtering using fetch() method
    """
    print("\n=== Fetching with Manual Filtering ===")

    try:
        dataset = braintrust.init_dataset(project=project_name, name="test123")

        # Use fetch() method to get records (allows for more control)
        print("Fetching records using fetch() method...")

        records = []
        for record in dataset.fetch():
            records.append(record)

        print(f"Fetched {len(records)} records using fetch() method")

        # Example: Filter records manually
        if records:
            # Filter records that have tags
            tagged_records = [r for r in records if r.get('tags')]
            print(f"Records with tags: {len(tagged_records)}")

            # Filter records by metadata
            records_with_metadata = [r for r in records if r.get('metadata')]
            print(f"Records with metadata: {len(records_with_metadata)}")

            # Filter by specific tag (test123)
            test123_records = filter_records_by_tag(records, "test123")
            print(f"Records with 'test123' tag: {len(test123_records)}")

    except Exception as e:
        print(f"Error fetching with filtering: {e}")

if __name__ == "__main__":
    print("=== Braintrust Dataset Fetcher & Filter ===")
    print("Fetching dataset 'test123'...\n")

    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project

    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME)

    # FIXED: Filter records by tag 'test123' (your original logic had a bug)
    print(f"\n=== Filtering Records by Tag 'test123' ===")
    filtered_records = filter_records_by_tag(records, "test123")

    print(f"Total records: {len(records)}")
    print(f"Filtered records (with 'test123' tag): {len(filtered_records)}")

    # Show filtered records
    for i, record in enumerate(filtered_records, 1):
        print(f"\nFiltered Record {i}:")
        print(f"  ID: {record.get('id', 'N/A')}")
        print(f"  Input: {record.get('input', 'N/A')}")
        print(f"  Expected: {record.get('expected', 'N/A')}")
        print(f"  Tags: {record.get('tags', 'N/A')}")

    # Create new dataset from filtered records
    if filtered_records:
        print(f"\n=== Creating New Dataset ===")
        new_dataset = create_dataset_from_records(
            records=filtered_records,
            project_name=PROJECT_NAME,
            new_dataset_name="test123_filtered_by_test123_tag",
            description="Records from test123 dataset filtered by 'test123' tag"
        )

        # Alternative: Use the complete workflow function
        print(f"\n=== Alternative: Complete Workflow Function ===")
        fetch_filter_and_create_dataset(
            source_project=PROJECT_NAME,
            source_dataset="test123",
            target_tag="test123",
            new_project=PROJECT_NAME,
            new_dataset_name="test123_workflow_filtered"
        )

    # Show how to use it in evaluations
    if records:
        use_dataset_in_evaluation(PROJECT_NAME)
        fetch_with_filtering(PROJECT_NAME)
    else:
        print("\nNo records found. Please check:")
        print("1. Dataset name is correct ('test123')")
        print("2. Project name is correct")
        print("3. You have access to the dataset")
        print("4. Your BRAINTRUST_API_KEY is set correctly")

    print("\n" + "="*60)
    print("USAGE EXAMPLES FOR CREATING FILTERED DATASETS:")
    print("="*60)
    print("""
# Example 1: Filter by different tag
fetch_filter_and_create_dataset(
    source_project='pedro-project1',
    source_dataset='test123',
    target_tag='important',
    new_dataset_name='important_records_only'
)

# Example 2: Create dataset in different project
fetch_filter_and_create_dataset(
    source_project='pedro-project1',
    source_dataset='test123',
    target_tag='test123',
    new_project='filtered_datasets_project',
    new_dataset_name='test123_subset'
)

# Example 3: Manual filtering and dataset creation
records = fetch_dataset_test123('pedro-project1')
filtered = filter_records_by_tag(records, 'my_tag')
create_dataset_from_records(
    records=filtered,
    project_name='pedro-project1',
    new_dataset_name='manually_filtered_dataset'
)
""")
