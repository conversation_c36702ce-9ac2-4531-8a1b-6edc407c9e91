"""
<PERSON><PERSON><PERSON> to fetch an existing dataset named "test123" from Braintrust
"""

import braintrust
from braintrust import Eval<PERSON>ase
from typing import List, Dict, Any

def fetch_dataset_test123(project_name, dataset_name) -> None:
    """
    Fetch and display records from the existing dataset "test123"
    
    Args:
        project_name: The project name where the dataset exists
    """
    try:
        print(f"Fetching dataset 'test123' from project '{project_name}'...")
        
        # Initialize connection to existing dataset
        dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
        records = []
        for record in dataset:
            records.append(record)

        return records
        
    except Exception as e:
        print(f"Error fetching dataset: {e}")
        print("\nPossible issues:")
        print("1. Dataset 'test123' doesn't exist")
        print("2. Project name is incorrect")
        print("3. You don't have access to this dataset")
        print("4. API key is not set or invalid")
        return []
    
def filter_records_by_tag(records: List[Dict[str, Any]], target_tag: str) -> List[Dict[str, Any]]:
    """
    Filter records that contain a specific tag

    Args:
        records: List of dataset records
        target_tag: The tag to filter by

    Returns:
        List of records that contain the target tag
    """
    filtered_records = []

    for record in records:
        tags = record.get('tags', [])
        # Check if tags exist and if target_tag is in the list of tags
        if tags and target_tag in tags:
            filtered_records.append(record)

    return filtered_records

def create_dataset_from_records(
    records: List[Dict[str, Any]],
    project_name: str,
    new_dataset_name: str,
    description: str = None
) -> None:
    """
    Create a new dataset from a list of records

    Args:
        records: List of dataset records to insert
        project_name: Project name for the new dataset
        new_dataset_name: Name for the new dataset
        description: Optional description for the dataset
    """
    try:
        # Initialize new dataset
        new_dataset = braintrust.init_dataset(project=project_name, name=new_dataset_name)

        # Insert each record
        inserted_count = 0
        for record in records:
            try:
                # Insert record with all its fields
                record_id = new_dataset.insert(
                    input=record.get('input'),
                    expected=record.get('expected') or record.get('output'),  # Handle both field names
                    metadata=record.get('metadata'),
                    tags=record.get('tags')
                )
                inserted_count += 1
                print(f"Inserted record {inserted_count} with ID: {record_id}")

            except Exception as e:
                print(f"Error inserting record {inserted_count + 1}: {e}")

        # Flush to ensure all records are saved
        new_dataset.flush()

        print(f"\nSuccessfully created dataset '{new_dataset_name}' with {inserted_count} records")

        return new_dataset

    except Exception as e:
        print(f"Error creating dataset: {e}")
        return None

def run_eval_with_filtered_dataset(
    filtered_records: List[Dict[str, Any]],
    project_name: str = "pedro-project1",
    experiment_name: str = "Filtered Dataset Evaluation"
) -> None:
    """
    Run an evaluation using filtered dataset records with ambience_scorer and ambiance repro prompt

    Args:
        filtered_records: List of filtered dataset records
        project_name: Project name containing the scorer and prompt
        experiment_name: Name for the evaluation experiment
    """
    try:
        print(f"\n=== Running Evaluation with Filtered Dataset ===")
        print(f"Project: {project_name}")
        print(f"Experiment: {experiment_name}")
        print(f"Records to evaluate: {len(filtered_records)}")

        if not filtered_records:
            print("❌ No filtered records to evaluate")
            return

        # Fetch the existing ambience_scorer
        print("\n1. Fetching ambience_scorer...")
        try:
            # Method 1: Try to load scorer by slug
            ambience_scorer = braintrust.load_scorer(
                project_name=project_name,
                slug="ambience_scorer"  # Assuming this is the slug
            )
            print("   ✓ Successfully loaded ambience_scorer")
        except Exception as e:
            print(f"   ❌ Could not load ambience_scorer by slug: {e}")
            try:
                # Method 2: Try to load scorer by name
                ambience_scorer = braintrust.load_scorer(
                    project_name=project_name,
                    name="ambience_scorer"
                )
                print("   ✓ Successfully loaded ambience_scorer by name")
            except Exception as e2:
                print(f"   ❌ Could not load ambience_scorer by name: {e2}")
                print("   ⚠️  Using placeholder scorer instead")
                # Fallback: Create a placeholder scorer
                def ambience_scorer(output, expected, **kwargs):
                    from braintrust import Score
                    return Score(name="ambience_scorer_placeholder", score=0.5)

        # Fetch the existing ambiance repro prompt
        print("\n2. Fetching ambiance repro prompt...")
        try:
            # Method 1: Try to load prompt by slug
            ambiance_prompt = braintrust.load_prompt(
                project_name=project_name,
                slug="ambiance-repro"  # Assuming this is the slug
            )
            print("   ✓ Successfully loaded ambiance repro prompt")
        except Exception as e:
            print(f"   ❌ Could not load ambiance repro prompt by slug: {e}")
            try:
                # Method 2: Try to load prompt by name
                ambiance_prompt = braintrust.load_prompt(
                    project_name=project_name,
                    name="ambiance repro"
                )
                print("   ✓ Successfully loaded ambiance repro prompt by name")
            except Exception as e2:
                print(f"   ❌ Could not load ambiance repro prompt by name: {e2}")
                print("   ⚠️  Using placeholder task instead")
                # Fallback: Create a placeholder task
                def ambiance_task(input_data, hooks=None):
                    return f"Placeholder response for: {input_data}"
                ambiance_prompt = None

        # Create task function using the prompt
        def create_task_from_prompt():
            if ambiance_prompt:
                def task_function(input_data, hooks=None):
                    try:
                        # Use the prompt to generate response
                        result = ambiance_prompt.build(input_data)
                        # If it's a chat completion format, you'd call OpenAI here
                        # For now, return the built prompt as placeholder
                        return f"Response using ambiance repro prompt: {result}"
                    except Exception as e:
                        if hooks:
                            hooks.span.log(error=str(e))
                        return f"Error using prompt: {e}"
                return task_function
            else:
                return ambiance_task

        task_function = create_task_from_prompt()

        # Convert filtered records to EvalCase format
        print("\n3. Converting records to EvalCase format...")
        eval_cases = convert_to_evalcases(filtered_records)
        print(f"   ✓ Converted {len(eval_cases)} records")

        # Run the evaluation
        print(f"\n4. Running evaluation...")
        from braintrust import Eval

        results = Eval(
            project_name,
            data=eval_cases,
            task=task_function,
            scores=[ambience_scorer],
            experiment_name=experiment_name
        )

        print(f"   ✅ Evaluation completed!")
        print(f"   Results: {len(results.results)} test cases evaluated")

        # Show sample results
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results.results[:3], 1):
            print(f"\nResult {i}:")
            print(f"  Input: {result.input}")
            print(f"  Output: {result.output}")
            print(f"  Expected: {result.expected}")
            print(f"  Scores: {result.scores}")
            if result.error:
                print(f"  Error: {result.error}")

        return results

    except Exception as e:
        print(f"❌ Error running evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None

def convert_to_evalcases(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Convert dataset records to EvalCase format for evaluation

    Args:
        records: List of dataset records

    Returns:
        List of records in EvalCase format
    """
    eval_cases = []
    for record in records:
        # Extract the core fields from the record
        eval_case = {
            "input": record.get("input", ""),
            "expected": record.get("expected", record.get("output", "")),
            "metadata": record.get("metadata", {}),
            "tags": record.get("tags", [])
        }
        eval_cases.append(eval_case)

    return eval_cases

if __name__ == "__main__":
    # You can change the project name if needed
    PROJECT_NAME = "pedro-project1"  # Change this if your dataset is in a different project
    dataset_name = "test123"
    
    # Fetch the dataset
    records = fetch_dataset_test123(PROJECT_NAME,dataset_name)

    filtered_records = filter_records_by_tag(records, "test123")

    print(f"\nFiltered {len(filtered_records)} records with tag 'test123'")

    # Option 1: Create a new dataset from the filtered records
    # create_dataset_from_records(filtered_records, PROJECT_NAME, "filtered_by_tag_test123")

    # Option 2: Run evaluation using the filtered dataset
    if filtered_records:
        print("\nRunning evaluation with filtered dataset...")
        eval_results = run_eval_with_filtered_dataset(
            filtered_records=filtered_records,
            project_name=PROJECT_NAME,
            experiment_name="Test123 Tag Evaluation"
        )

        if eval_results:
            print(f"\n✅ Evaluation completed successfully!")
            print(f"Check the Braintrust UI for detailed results.")
        else:
            print(f"\n❌ Evaluation failed.")
    else:
        print("\n⚠️  No records found with tag 'test123' to evaluate.")